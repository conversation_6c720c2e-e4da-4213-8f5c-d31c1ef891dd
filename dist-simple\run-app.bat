@echo off
echo ========================================
echo    تطبيق عيادة الدكتور يونس اسود الجبوري
echo    Dr. Younes Aswad Al-Jubouri Clinic App
echo ========================================
echo.

echo تحقق من وجود Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo خطأ: Node.js غير مثبت على هذا الجهاز
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo تحقق من وجود npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo خطأ: npm غير متوفر
    echo يرجى إعادة تثبيت Node.js
    echo.
    pause
    exit /b 1
)

echo.
echo تثبيت التبعيات المطلوبة...
call npm install electron --save-dev
if %errorlevel% neq 0 (
    echo.
    echo خطأ في تثبيت Electron
    echo تحقق من اتصال الإنترنت وحاول مرة أخرى
    echo.
    pause
    exit /b 1
)

echo.
echo تشغيل التطبيق...
call npm start

if %errorlevel% neq 0 (
    echo.
    echo خطأ في تشغيل التطبيق
    echo تحقق من الملفات وحاول مرة أخرى
    echo.
    pause
    exit /b 1
)

echo.
echo تم إغلاق التطبيق بنجاح
pause
