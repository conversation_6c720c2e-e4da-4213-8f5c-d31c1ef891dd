# دليل التثبيت والاستخدام - تطبيق عيادة الدكتور يونس

## نظرة عامة
هذا الدليل يوضح كيفية تثبيت واستخدام تطبيق إدارة حجوزات عيادة الدكتور يونس اسود الجبوري على منصات مختلفة.

## التثبيت على Windows

### الطريقة الأولى: استخدام الملف التنفيذي (الأسهل)

1. **تحميل التطبيق:**
   - احصل على ملف `عيادة-الدكتور-يونس-Setup.exe` من مجلد `dist/`
   - أو قم بتشغيل `build-scripts/build-windows.bat` لبناء التطبيق

2. **التثبيت:**
   - انقر نقراً مزدوجاً على ملف التثبيت
   - اتبع خطوات المعالج:
     - اختر مجلد التثبيت (افتراضي: `C:\Program Files\عيادة الدكتور يونس`)
     - اختر إنشاء اختصار على سطح المكتب
     - اختر إضافة التطبيق لقائمة ابدأ
   - انقر "تثبيت" وانتظر انتهاء العملية

3. **التشغيل:**
   - انقر على أيقونة التطبيق من سطح المكتب
   - أو ابحث عن "عيادة الدكتور يونس" في قائمة ابدأ

### الطريقة الثانية: التشغيل من المصدر

1. **المتطلبات:**
   - Node.js (الإصدار 16 أو أحدث)
   - Git (اختياري)

2. **التحميل والتثبيت:**
   ```cmd
   # تحميل المشروع
   git clone [رابط المشروع]
   cd dr-clinic-app
   
   # تثبيت التبعيات
   npm install
   
   # تشغيل التطبيق
   npm start
   ```

## التثبيت على Android

### الطريقة الأولى: تثبيت APK مباشرة

1. **تحضير الجهاز:**
   - اذهب إلى الإعدادات > الأمان
   - فعّل "مصادر غير معروفة" أو "تثبيت تطبيقات غير معروفة"

2. **التثبيت:**
   - انسخ ملف `clinic-app.apk` إلى هاتفك
   - انقر على الملف واتبع خطوات التثبيت
   - اقبل الأذونات المطلوبة

3. **التشغيل:**
   - ابحث عن أيقونة "عيادة الدكتور يونس" في قائمة التطبيقات
   - انقر لتشغيل التطبيق

### الطريقة الثانية: بناء APK من المصدر

1. **المتطلبات:**
   - Node.js (الإصدار 16 أو أحدث)
   - Android Studio
   - Java Development Kit (JDK) 11

2. **الإعداد:**
   ```bash
   # تثبيت Capacitor
   npm install -g @capacitor/cli
   
   # تهيئة المشروع
   cd mobile-version
   npm init -y
   npm install @capacitor/core @capacitor/android
   
   # تهيئة Capacitor
   npx cap init "عيادة الدكتور يونس" "com.clinic.dr.appointments"
   
   # إضافة منصة Android
   npx cap add android
   ```

3. **البناء:**
   ```bash
   # نسخ ملفات الويب
   npx cap copy
   
   # فتح في Android Studio
   npx cap open android
   
   # في Android Studio:
   # Build > Generate Signed Bundle / APK > APK
   ```

## الاستخدام

### الواجهة الرئيسية

1. **أزرار الأيام:**
   - يعرض التطبيق 8 أيام (اليوم الحالي + 7 أيام قادمة)
   - أيام السبت والأربعاء مقسمة إلى فترتين (صباحاً/مساءً)
   - انقر على أي يوم لعرض حجوزاته

2. **إدارة الحجوزات:**
   - **إضافة حجز:** انقر "إضافة حجز" أو الزر "+"
   - **تعديل حجز:** انقر على الحجز المطلوب
   - **حذف حجز:** انقر على زر الحذف (🗑️)
   - **تغيير الحالة:** انقر على الحجز لتغيير لونه

### أنواع الجلسات

1. **جلسة دائمية:** جلسة عادية بدون تفاصيل إضافية
2. **جذر:** جلسة علاج جذور مع رقم الجلسة (1-4)

### نظام الألوان

- **شفاف:** حجز جديد
- **أخضر:** تم الانتهاء
- **أصفر:** قيد المعالجة
- **أحمر:** ملغي أو مؤجل

### الميزات الإضافية

1. **البحث:**
   - انقر "جميع الحجوزات"
   - استخدم مربع البحث للعثور على مريض معين

2. **التصدير:**
   - انقر "تصدير إلى Word"
   - سيتم تحميل ملف Word يحتوي على جميع الحجوزات

3. **الإعدادات:**
   - تغيير حجم الخط
   - تغيير نوع الخط
   - تفعيل/إلغاء الوضع الليلي

## حل المشاكل الشائعة

### مشاكل Windows

1. **التطبيق لا يبدأ:**
   - تأكد من تثبيت Visual C++ Redistributable
   - شغّل التطبيق كمدير

2. **خطأ في التثبيت:**
   - تأكد من وجود مساحة كافية (50 ميجابايت على الأقل)
   - أغلق برامج مكافحة الفيروسات مؤقتاً

### مشاكل Android

1. **التطبيق لا يثبت:**
   - تأكد من تفعيل "مصادر غير معروفة"
   - تأكد من وجود مساحة كافية (20 ميجابايت)

2. **التطبيق يتوقف:**
   - أعد تشغيل الجهاز
   - امسح cache التطبيق من الإعدادات

### مشاكل عامة

1. **فقدان البيانات:**
   - البيانات محفوظة محلياً في التطبيق
   - لا تحذف التطبيق بدون نسخ احتياطي

2. **بطء في الأداء:**
   - أغلق التطبيقات الأخرى
   - أعد تشغيل التطبيق

## النسخ الاحتياطي والاستعادة

### Windows:
- البيانات محفوظة في: `%APPDATA%\عيادة الدكتور يونس\`
- انسخ مجلد البيانات للنسخ الاحتياطي

### Android:
- استخدم ميزة "تصدير البيانات" في التطبيق
- احفظ الملف المُصدّر في مكان آمن

## الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من وجود تحديثات للتطبيق
3. تواصل مع فريق الدعم مع تفاصيل المشكلة

## معلومات إضافية

- **الإصدار:** 1.0.0
- **آخر تحديث:** [التاريخ]
- **الحجم:** ~15 ميجابايت (Windows) / ~8 ميجابايت (Android)
- **المتطلبات:** Windows 10+ / Android 7.0+
