@echo off
echo ========================================
echo    إنشاء حزمة التوزيع
echo    Creating Distribution Package
echo ========================================
echo.

set DIST_NAME=Dr-Clinic-App-v1.0.0
set CURRENT_DIR=%cd%

echo إنشاء مجلد التوزيع...
if exist "%DIST_NAME%" rmdir /s /q "%DIST_NAME%"
mkdir "%DIST_NAME%"

echo نسخ الملفات الأساسية...
copy "index.html" "%DIST_NAME%\"
copy "script.js" "%DIST_NAME%\"
copy "styles.css" "%DIST_NAME%\"
copy "main.js" "%DIST_NAME%\"
copy "package.json" "%DIST_NAME%\"
copy "run-app.bat" "%DIST_NAME%\"
copy "web-version.html" "%DIST_NAME%\"
copy "README-SIMPLE.md" "%DIST_NAME%\"
copy "INSTRUCTIONS.md" "%DIST_NAME%\"

echo نسخ مجلد الأصول...
xcopy "assets" "%DIST_NAME%\assets\" /E /I /Q

echo نسخ ملفات الموبايل...
xcopy "mobile" "%DIST_NAME%\mobile\" /E /I /Q

echo إنشاء ملف معلومات الإصدار...
echo تطبيق عيادة الدكتور يونس اسود الجبوري > "%DIST_NAME%\VERSION.txt"
echo الإصدار: 1.0.0 >> "%DIST_NAME%\VERSION.txt"
echo تاريخ البناء: %date% %time% >> "%DIST_NAME%\VERSION.txt"
echo نوع الحزمة: توزيع مبسط >> "%DIST_NAME%\VERSION.txt"
echo. >> "%DIST_NAME%\VERSION.txt"
echo محتويات الحزمة: >> "%DIST_NAME%\VERSION.txt"
echo - تطبيق سطح المكتب (Electron) >> "%DIST_NAME%\VERSION.txt"
echo - نسخة ويب مستقلة >> "%DIST_NAME%\VERSION.txt"
echo - ملفات الموبايل (Capacitor) >> "%DIST_NAME%\VERSION.txt"
echo - تعليمات التشغيل والتوزيع >> "%DIST_NAME%\VERSION.txt"

echo إنشاء ملف تشغيل سريع...
echo @echo off > "%DIST_NAME%\START-HERE.bat"
echo echo ======================================== >> "%DIST_NAME%\START-HERE.bat"
echo echo    تطبيق عيادة الدكتور يونس اسود الجبوري >> "%DIST_NAME%\START-HERE.bat"
echo echo    اختر طريقة التشغيل: >> "%DIST_NAME%\START-HERE.bat"
echo echo ======================================== >> "%DIST_NAME%\START-HERE.bat"
echo echo. >> "%DIST_NAME%\START-HERE.bat"
echo echo [1] تشغيل تطبيق سطح المكتب >> "%DIST_NAME%\START-HERE.bat"
echo echo [2] فتح النسخة الويب >> "%DIST_NAME%\START-HERE.bat"
echo echo [3] قراءة التعليمات >> "%DIST_NAME%\START-HERE.bat"
echo echo. >> "%DIST_NAME%\START-HERE.bat"
echo set /p choice="اختر رقم (1-3): " >> "%DIST_NAME%\START-HERE.bat"
echo. >> "%DIST_NAME%\START-HERE.bat"
echo if "%%choice%%"=="1" start run-app.bat >> "%DIST_NAME%\START-HERE.bat"
echo if "%%choice%%"=="2" start web-version.html >> "%DIST_NAME%\START-HERE.bat"
echo if "%%choice%%"=="3" start INSTRUCTIONS.md >> "%DIST_NAME%\START-HERE.bat"
echo. >> "%DIST_NAME%\START-HERE.bat"
echo pause >> "%DIST_NAME%\START-HERE.bat"

echo ضغط الحزمة...
if exist "%DIST_NAME%.zip" del "%DIST_NAME%.zip"
powershell -command "Compress-Archive -Path '%DIST_NAME%' -DestinationPath '%DIST_NAME%.zip'"

echo.
echo ========================================
echo ✓ تم إنشاء حزمة التوزيع بنجاح!
echo.
echo الملفات المُنشأة:
echo - مجلد: %DIST_NAME%
echo - ملف مضغوط: %DIST_NAME%.zip
echo.
echo لتوزيع التطبيق:
echo 1. شارك الملف المضغوط مع المستخدمين
echo 2. أو انسخ مجلد %DIST_NAME% كاملاً
echo 3. اطلب من المستخدمين تشغيل START-HERE.bat
echo ========================================
echo.

echo فتح مجلد النتائج...
start explorer .

pause
