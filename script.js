// تحميل البيانات من localStorage
let appointmentsData = {};
if (localStorage.getItem('appointmentsData')) {
  appointmentsData = JSON.parse(localStorage.getItem('appointmentsData'));
}

// دالة لحفظ البيانات في localStorage
function saveData() {
  localStorage.setItem('appointmentsData', JSON.stringify(appointmentsData));
}

// دالة لإرجاع التاريخ بصيغة "YYYY-MM-DD"
function getLocalDateStr(dateObj) {
  return dateObj.getFullYear() + "-" +
         ("0" + (dateObj.getMonth() + 1)).slice(-2) + "-" +
         ("0" + dateObj.getDate()).slice(-2);
}

const arabicDays = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"];

let currentEditDay = null;
let currentEditIndex = null;
let currentSelectedDay = null;

// دالة لمسح البيانات التي مضى عليها أكثر من شهر
function purgeExpiredData() {
  const today = new Date();
  const expiryDate = new Date();
  expiryDate.setMonth(expiryDate.getMonth() - 3);
  Object.keys(appointmentsData).forEach(dateStr => {
    const dateObj = new Date(dateStr);
    if (isNaN(dateObj)||dateObj < expiryDate) {
      delete appointmentsData[dateStr];
    }
  });
  saveData();
}

document.addEventListener("DOMContentLoaded", () => {
  purgeExpiredData();
  generateDayButtons();
  setupModal();
  setupSessionTypeChange();
  setupSearch();
  setupEditModal();
  setupSettings();

  document.getElementById("add-appointment-btn").addEventListener("click", () => {
    openModal(currentSelectedDay);
  });
  
  document.getElementById("export-word-btn").addEventListener("click", exportToWord);
  
  // زر عرض جميع الحجوزات
  document.getElementById("show-all-btn").addEventListener("click", openAllAppointmentsModal);
  
  // إضافة اختصار في الكيبورد: Ctrl + Shift + A لفتح زر "إضافة حجز"
  document.addEventListener("keydown", (e) => {
    if (e.altKey&&e.key.toLowerCase() === "ش") {
      e.preventDefault();
      if (currentSelectedDay) {
        document.getElementById("add-appointment-btn").click();
      } else {
        alert("الرجاء اختيار يوم أولاً");
      }
    }
  });
});

function setupSearch() {
  const searchInput = document.getElementById("search-input");
  searchInput.addEventListener("input", () => {
    updateAppointmentsDisplay(currentSelectedDay);
  });
}

function generateDayButtons() {
  const daysContainer = document.getElementById("days-container");
  daysContainer.innerHTML = "";
  const today = new Date();
  
  for (let i = 0; i < 8; i++) {
    const dayDate = new Date(today);
    dayDate.setDate(today.getDate() + i);
    const dayName = arabicDays[dayDate.getDay()] || "يوم";
    const dateStr = dayDate.toLocaleDateString("ar-EG");
    const local_date_str = getLocalDateStr(dayDate);
    
    const dayButton = document.createElement("div");
    dayButton.className = "day-button";
    dayButton.dataset.date = local_date_str;
    dayButton.innerHTML = `<strong>${dayName}</strong><br>${dateStr}`;
    
    if (!appointmentsData[dayButton.dataset.date]) {
      appointmentsData[dayButton.dataset.date] = [];
    }
    
    dayButton.addEventListener("click", (e) => {
      document.querySelectorAll(".day-button").forEach(btn => btn.classList.remove("selected"));
      dayButton.classList.add("selected");
      currentSelectedDay = dayButton.dataset.date;
      updateAppointmentsDisplay(currentSelectedDay);
      e.stopPropagation();
    });
    
    daysContainer.appendChild(dayButton);
  }
  saveData();
}

function updateAppointmentsDisplay(date) {
  const display = document.getElementById("appointments-display");
  display.innerHTML = "";
  if (!date || !appointmentsData[date] || appointmentsData[date].length === 0) {
    display.textContent = "لا يوجد حجوزات لهذا اليوم.";
    return;
  }
  
  const searchTerm = document.getElementById("search-input").value.trim().toLowerCase();
  
  appointmentsData[date].forEach((appointment, index) => {
    if (searchTerm && !appointment.patient_name.toLowerCase().includes(searchTerm)) {
      return;
    }
    
    const appDiv = document.createElement("div");
    appDiv.className = "appointment";
    
    const seqSpan = document.createElement("span");
    seqSpan.className = "sequence";
    seqSpan.textContent = index + 1;
    appDiv.appendChild(seqSpan);
    
    const procedureContainer = document.createElement("div");
    procedureContainer.className = "procedure-container";
    
    const syringe = document.createElement("span");
    syringe.className = "syringe";
    syringe.textContent = "💉";
    procedureContainer.appendChild(syringe);
    
    const procedureNumberSpan = document.createElement("span");
    procedureNumberSpan.className = "procedure-number-display";
    if (appointment.procedureNumber) {
      procedureNumberSpan.textContent = appointment.procedureNumber;
    }
    procedureContainer.appendChild(procedureNumberSpan);
    appDiv.appendChild(procedureContainer);
    
    const infoContainer = document.createElement("div");
    infoContainer.className = "info-container";
    infoContainer.style.backgroundColor = getColorByState(appointment.colorState);
    infoContainer.addEventListener("click", (e) => {
      appointment.colorState = (appointment.colorState + 1) % 4;
      infoContainer.style.backgroundColor = getColorByState(appointment.colorState);
      e.stopPropagation();
      saveData();
    });
    
    const nameDiv = document.createElement("div");
    nameDiv.className = "patient-name";
    nameDiv.textContent = appointment.patient_name;
    infoContainer.appendChild(nameDiv);
    
    const detailsDiv = document.createElement("div");
    detailsDiv.className = "details";
    detailsDiv.textContent = `- ${appointment.session_type}` + 
      (appointment.session_type === "جذر" && appointment.session_number ? ` (${appointment.session_number})` : "");
    infoContainer.appendChild(detailsDiv);
    
    if (appointment.note) {
      const noteDiv = document.createElement("div");
      noteDiv.className = "details";
      noteDiv.textContent = `- ملاحظة: ${appointment.note}`;
      infoContainer.appendChild(noteDiv);
    }
    
    appDiv.appendChild(infoContainer);
    
    const editBtn = document.createElement("button");
    editBtn.innerHTML = '<i class="fas fa-edit"></i>';
    editBtn.className = "btn edit";
    editBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      openEditModal(currentSelectedDay, index);
    });
    appDiv.appendChild(editBtn);
    
    const deleteBtn = document.createElement("button");
    deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
    deleteBtn.className = "btn delete";
    deleteBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      appointmentsData[currentSelectedDay].splice(index, 1);
      updateAppointmentsDisplay(currentSelectedDay);
      saveData();
    });
    appDiv.appendChild(deleteBtn);
    
    syringe.addEventListener("click", (e) => {
      e.stopPropagation();
      const existingPopup = appDiv.querySelector(".procedure-popup");
      if (existingPopup) {
        existingPopup.remove();
        return;
      }
      const popup = createProcedurePopup(appointment, procedureNumberSpan);
      syringe.parentElement.appendChild(popup);
    });
    
    display.appendChild(appDiv);
  });
  saveData();
}

function setupModal() {
  const modal = document.getElementById("appointment-modal");
  const closeButton = document.getElementById("add-close-button");
  const form = document.getElementById("appointment-form");

  closeButton.addEventListener("click", () => {
    modal.style.display = "none";
    form.reset();
    document.getElementById("root-session-number").style.display = "none";
  });

  window.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.style.display = "none";
      form.reset();
      document.getElementById("root-session-number").style.display = "none";
    }
  });

  form.addEventListener("submit", (e) => {
    e.preventDefault();
    addAppointment();
    modal.style.display = "none";
    form.reset();
    document.getElementById("root-session-number").style.display = "none";
  });
}

function openModal(date) {
  document.getElementById("selected-day").value = date;
  document.getElementById("appointment-modal").style.display = "block";
}

function setupEditModal() {
  const editModal = document.getElementById("edit-modal");
  const editClose = document.getElementById("edit-close-button");
  const editForm = document.getElementById("edit-form");

  editClose.addEventListener("click", () => {
    editModal.style.display = "none";
    editForm.reset();
    document.getElementById("edit-root-session-number").style.display = "none";
  });

  window.addEventListener("click", (e) => {
    if (e.target === editModal) {
      editModal.style.display = "none";
      editForm.reset();
      document.getElementById("edit-root-session-number").style.display = "none";
    }
  });

  editForm.addEventListener("submit", (e) => {
    e.preventDefault();
    let appointment = appointmentsData[currentEditDay][currentEditIndex];
    appointment.patient_name = document.getElementById("edit-patient-name").value;
    appointment.note = document.getElementById("edit-appointment-note").value;
    appointment.session_type = document.getElementById("edit-session-type").value;
    if (appointment.session_type === "جذر") {
      appointment.session_number = document.getElementById("edit-session-number").value;
    } else {
      appointment.session_number = "";
    }
    updateAppointmentsDisplay(currentEditDay);
    saveData();
    editModal.style.display = "none";
    editForm.reset();
    document.getElementById("edit-root-session-number").style.display = "none";
  });

  const editSessionType = document.getElementById("edit-session-type");
  editSessionType.addEventListener("change", () => {
    if (editSessionType.value === "جذر") {
      document.getElementById("edit-root-session-number").style.display = "block";
    } else {
      document.getElementById("edit-root-session-number").style.display = "none";
    }
  });
}

function openEditModal(day, index) {
  currentEditDay = day;
  currentEditIndex = index;
  let appointment = appointmentsData[day][index];

  document.getElementById("edit-patient-name").value = appointment.patient_name;
  document.getElementById("edit-appointment-note").value = appointment.note || "";
  document.getElementById("edit-session-type").value = appointment.session_type;
  if (appointment.session_type === "جذر") {
    document.getElementById("edit-root-session-number").style.display = "block";
    document.getElementById("edit-session-number").value = appointment.session_number || "1";
  } else {
    document.getElementById("edit-root-session-number").style.display = "none";
  }
  
  document.getElementById("edit-modal").style.display = "block";
}

function setupSessionTypeChange() {
  const sessionTypeSelect = document.getElementById("session-type");
  sessionTypeSelect.addEventListener("change", () => {
    if (sessionTypeSelect.value === "جذر") {
      document.getElementById("root-session-number").style.display = "block";
    } else {
      document.getElementById("root-session-number").style.display = "none";
    }
  });
}

function addAppointment() {
  const patientName = document.getElementById("patient-name").value;
  const note = document.getElementById("appointment-note").value;
  const sessionType = document.getElementById("session-type").value;
  const selectedDay = document.getElementById("selected-day").value;
  let sessionNumber = "";
  if (sessionType === "جذر") {
    sessionNumber = document.getElementById("session-number").value;
  }
  
  const appointment = {
    patient_name: patientName,
    note: note,
    session_type: sessionType,
    session_number: sessionNumber,
    procedureNumber: null,
    colorState: 0
  };

  if (!appointmentsData[selectedDay]) {
    appointmentsData[selectedDay] = [];
  }
  appointmentsData[selectedDay].push(appointment);
  updateAppointmentsDisplay(selectedDay);
  saveData();
}

function getColorByState(state) {
  switch (state) {
    case 1: return "#008000"; // أخضر فاتح
    case 2: return "#b7990c"; // أصفر
    case 3: return "#f44336"; // أحمر
    default: return "transparent";
  }
}

function createProcedurePopup(appointment, procedureNumberSpan) {
  const popup = document.createElement("div");
  popup.className = "procedure-popup";
  for (let i = 0; i <= 10; i++) {
    const numSpan = document.createElement("span");
    numSpan.textContent = i;
    numSpan.style.cursor = "pointer";
    numSpan.style.padding = "2px 5px";
    numSpan.addEventListener("click", (e) => {
      if (i === 0) {
        appointment.procedureNumber = null;
        procedureNumberSpan.textContent = "";
      } else {
        appointment.procedureNumber = i;
        procedureNumberSpan.textContent = i;
      }
      popup.remove();
      e.stopPropagation();
      saveData();
    });
    popup.appendChild(numSpan);
  }
  return popup;
}

function exportToWord() {
  let html = "<html><head><meta charset='utf-8'><title>حجوزات مراجعين</title></head><body>";
  html += "<h1>حجوزات مراجعين</h1>";
  for (const day in appointmentsData) {
    if (appointmentsData.hasOwnProperty(day)) {
      let dateObj = new Date(day);
      let dayName = arabicDays[dateObj.getDay()];
      html += `<h2>يوم: ${dayName} - ${day}</h2>`;
      html += "<table border='1' style='border-collapse:collapse; width:100%;'>";
      html += "<tr><th>رقم التسلسل</th><th>اسم المريض</th><th>ملاحظة</th><th>نوع الجلسة</th><th>رقم الجلسة</th><th>رقم الإجراء</th><th>الحالة</th></tr>";
      appointmentsData[day].forEach((appointment, index) => {
        let status = appointment.colorState === 3 ? "تم" : "لم يتم";
        html += `<tr>
          <td>${index + 1}</td>
          <td>${appointment.patient_name}</td>
          <td>${appointment.note ? appointment.note : ""}</td>
          <td>${appointment.session_type}</td>
          <td>${appointment.session_type === "جذر" ? appointment.session_number : ""}</td>
          <td>${appointment.procedureNumber ? appointment.procedureNumber : ""}</td>
          <td>${status}</td>
        </tr>`;
      });
      html += "</table>";
    }
  }
  html += "</body></html>";
  
  const blob = new Blob([html], { type: "application/msword" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  let today = new Date();
  let dateStr = getLocalDateStr(today);
  let dayName = arabicDays[today.getDay()];
  a.download = `${dayName}_${dateStr}.doc`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// وظيفة تحديث محتوى مودال "جميع الحجوزات" بناءً على البحث
function updateAllAppointmentsContent() {
  const contentDiv = document.getElementById("all-appointments-content");
  contentDiv.innerHTML = "";
  const query = document.getElementById("all-appointments-search").value.trim().toLowerCase();
  
  // ترتيب التواريخ تصاعدياً
  const dates = Object.keys(appointmentsData).sort();
  dates.forEach(date => {
    // فلترة الحجوزات داخل التاريخ بناءً على البحث
    const filteredApps = appointmentsData[date].filter(appointment =>
      appointment.patient_name.toLowerCase().includes(query)
    );
    if (filteredApps.length > 0) {
      let dateObj = new Date(date);
      let dayName = arabicDays[dateObj.getDay()];
      const header = document.createElement("h3");
      header.textContent = `${dayName} - ${date}`;
      contentDiv.appendChild(header);
      
      filteredApps.forEach((appointment, index) => {
        const appCard = document.createElement("div");
        appCard.className = "appointment-card";
        // تلوين البطاقة بالكامل بناءً على الحالة المخزنة
        appCard.style.backgroundColor = getColorByState(appointment.colorState);
        appCard.innerHTML = `<strong>${index + 1}. ${appointment.patient_name}</strong> - ${appointment.session_type}` +
          (appointment.session_type === "جذر" && appointment.session_number ? ` (${appointment.session_number})` : "") +
          (appointment.note ? ` - ملاحظة: ${appointment.note}` : "");
        contentDiv.appendChild(appCard);
      });
    }
  });
}

function openAllAppointmentsModal() {
  // عند فتح المودال نحدث المحتوى أولاً
  updateAllAppointmentsContent();
  const modal = document.getElementById("all-appointments-modal");
  modal.style.display = "block";
  // إضافة مستمع لحقل البحث داخل المودال لتحديث المحتوى أثناء الكتابة
  document.getElementById("all-appointments-search").addEventListener("input", updateAllAppointmentsContent);
}

// إغلاق مودال "جميع الحجوزات"
document.getElementById("all-close-button").addEventListener("click", () => {
  document.getElementById("all-appointments-modal").style.display = "none";
});

window.addEventListener("click", (e) => {
  const allModal = document.getElementById("all-appointments-modal");
  if (e.target === allModal) {
    allModal.style.display = "none";
  }
});

function setupSettings() {
  const settingsBtn = document.getElementById("settingsBtn");
  const settingsPanel = document.getElementById("settingsPanel");
  
  settingsBtn.addEventListener("click", () => {
    settingsPanel.classList.toggle("hidden");
  });
  
  document.getElementById("fontSize").addEventListener("input", function() {
    document.body.style.fontSize = this.value + "px";
  });
  
  document.getElementById("fontFamily").addEventListener("change", function() {
    document.body.style.fontFamily = this.value;
  });
  
  document.getElementById("darkMode").addEventListener("change", function() {
    document.body.classList.toggle("dark-mode", this.checked);
  });
}
