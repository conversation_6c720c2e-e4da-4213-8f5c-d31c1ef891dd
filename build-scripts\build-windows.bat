@echo off
echo ========================================
echo    بناء تطبيق عيادة الدكتور يونس
echo    Windows Executable Builder
echo ========================================
echo.

echo [1/5] التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت. يرجى تثبيت Node.js أولاً
    pause
    exit /b 1
)
echo ✓ Node.js مثبت

echo.
echo [2/5] التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: npm غير متوفر
    pause
    exit /b 1
)
echo ✓ npm متوفر

echo.
echo [3/5] تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo خطأ في تثبيت التبعيات
    pause
    exit /b 1
)
echo ✓ تم تثبيت التبعيات

echo.
echo [4/5] تثبيت electron-builder...
call npm install --save-dev electron-builder
if %errorlevel% neq 0 (
    echo خطأ في تثبيت electron-builder
    pause
    exit /b 1
)
echo ✓ تم تثبيت electron-builder

echo.
echo [5/5] بناء التطبيق...
call npm run build-win
if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✓ تم بناء التطبيق بنجاح!
echo ملفات التطبيق موجودة في مجلد: dist/
echo ========================================
echo.

echo فتح مجلد النتائج...
start explorer dist

pause
