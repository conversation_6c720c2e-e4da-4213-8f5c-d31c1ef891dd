# تطبيق إدارة حجوزات عيادة الدكتور يونس اسود الجبوري

## نظرة عامة

تطبيق شامل لإدارة حجوزات المرضى في عيادة الدكتور يونس اسود الجبوري. يوفر التطبيق واجهة سهلة الاستخدام لتنظيم المواعيد وتتبع حالة المرضى مع دعم كامل للغة العربية.

## الميزات الرئيسية

### 📅 إدارة المواعيد
- عرض 8 أيام (اليوم الحالي + 7 أيام قادمة)
- تقسيم أيام السبت والأربعاء إلى فترتين (صباحاً/مساءً)
- إضافة وتعديل وحذف الحجوزات بسهولة
- نظام ألوان لتتبع حالة المرضى

### 🏥 أنواع الجلسات
- **جلسة دائمية:** للمعالجات العادية
- **جذر:** لعلاج الجذور مع تتبع رقم الجلسة (1-4)

### 🎨 نظام الألوان
- **شفاف:** حجز جديد
- **أخضر:** تم الانتهاء من العلاج
- **أصفر:** قيد المعالجة
- **أحمر:** ملغي أو مؤجل

### 🔍 البحث والتصدير
- البحث السريع عن المرضى بالاسم
- تصدير جميع البيانات إلى ملف Word
- عرض جميع الحجوزات في واجهة منظمة

### ⚙️ الإعدادات المتقدمة
- تخصيص حجم ونوع الخط
- الوضع الليلي/النهاري
- حفظ تلقائي للبيانات

## المنصات المدعومة

### 🖥️ Windows
- ملف تنفيذي (.exe) مع installer
- دعم Windows 10/11 (32-bit & 64-bit)
- تشغيل مستقل بدون تبعيات خارجية

### 📱 Android
- تطبيق APK أصلي
- واجهة محسنة للشاشات اللمسية
- دعم Android 7.0 فما فوق
- تشغيل offline كامل

## التقنيات المستخدمة

- **Frontend:** HTML5, CSS3, JavaScript (ES6+)
- **Desktop:** Electron
- **Mobile:** Capacitor
- **Storage:** LocalStorage / IndexedDB
- **Build Tools:** electron-builder, Capacitor CLI

## هيكل المشروع

```
dr-clinic-app/
├── assets/                 # الأيقونات والصور
├── build-scripts/          # سكريبت البناء
├── mobile-version/         # نسخة الموبايل
├── dist/                   # ملفات التوزيع
├── index.html              # الواجهة الرئيسية
├── script.js               # المنطق الأساسي
├── styles.css              # التنسيقات
├── main.js                 # Electron main process
├── package.json            # تبعيات المشروع
├── capacitor.config.ts     # إعدادات Capacitor
├── BUILD_GUIDE.md          # دليل البناء
├── INSTALLATION_GUIDE.md   # دليل التثبيت
└── README.md               # هذا الملف
```

## البدء السريع

### للمطورين

```bash
# استنساخ المشروع
git clone [repository-url]
cd dr-clinic-app

# تثبيت التبعيات
npm install

# تشغيل التطبيق في وضع التطوير
npm start

# بناء التطبيق للتوزيع
npm run build-win          # Windows
npm run build-android      # Android
```

### للمستخدمين

1. **Windows:** قم بتشغيل `build-scripts/build-windows.bat`
2. **Android:** اتبع الخطوات في `BUILD_GUIDE.md`

## الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `BUILD_GUIDE.md` | دليل شامل لبناء التطبيق للمنصات المختلفة |
| `INSTALLATION_GUIDE.md` | تعليمات التثبيت والاستخدام |
| `build-scripts/build-windows.bat` | سكريبت تلقائي لبناء نسخة Windows |
| `mobile-version/` | ملفات النسخة المحسنة للموبايل |
| `capacitor.config.ts` | إعدادات تطبيق Android |

## المتطلبات

### للتطوير:
- Node.js 16+
- npm أو yarn
- Git (اختياري)

### لبناء Windows:
- Windows 10/11
- Visual Studio Build Tools (تلقائي مع Node.js)

### لبناء Android:
- Android Studio
- Java Development Kit (JDK) 11+
- Android SDK

## الاستخدام

### إضافة حجز جديد
1. اختر اليوم المطلوب
2. انقر "إضافة حجز"
3. أدخل بيانات المريض
4. اختر نوع الجلسة
5. احفظ الحجز

### تتبع حالة المريض
1. انقر على الحجز
2. اختر اللون المناسب للحالة
3. أضف ملاحظات إضافية عند الحاجة

### تصدير البيانات
1. انقر "تصدير إلى Word"
2. احفظ الملف في المكان المطلوب
3. استخدم الملف للطباعة أو الأرشفة

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً على الجهاز
- لا يتم إرسال أي بيانات عبر الإنترنت
- النسخ الاحتياطي يدوي وتحت سيطرة المستخدم
- تشفير البيانات الحساسة (في الإصدارات المستقبلية)

## الدعم والصيانة

### الإبلاغ عن المشاكل
- راجع `INSTALLATION_GUIDE.md` أولاً
- تأكد من استخدام أحدث إصدار
- قدم تفاصيل واضحة عن المشكلة

### طلب ميزات جديدة
- اقترح الميزات الجديدة مع شرح الحاجة إليها
- قدم أمثلة على كيفية استخدام الميزة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. تطبيق التغييرات مع اختبارها
4. إرسال Pull Request

## الإصدارات

- **v1.0.0** - الإصدار الأولي
  - إدارة أساسية للحجوزات
  - دعم Windows و Android
  - واجهة عربية كاملة

## شكر خاص

- الدكتور يونس اسود الجبوري لثقته في المشروع
- جميع المساهمين في تطوير التطبيق
- مجتمع المطورين العرب للدعم والمساعدة

---

**تطوير:** فريق تطوير تطبيقات العيادات الطبية  
**التحديث الأخير:** يوليو 2025  
**الإصدار:** 1.0.0
