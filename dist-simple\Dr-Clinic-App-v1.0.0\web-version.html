<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عيادة الدكتور يونس اسود الجبوري</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* تحسينات خاصة بالنسخة الويب */
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
            min-height: 100vh;
        }
        
        .web-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .web-header h1 {
            color: #00aaff;
            margin: 0;
            font-size: 2em;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .web-header p {
            color: #ccc;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        
        .web-footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #888;
            border-top: 1px solid #444;
        }
        
        .web-footer p {
            margin: 5px 0;
        }
        
        /* تحسينات للطباعة */
        @media print {
            .web-header, .web-footer {
                display: none;
            }
            
            body {
                background: white;
                color: black;
            }
            
            .day-button {
                border: 1px solid #000;
                background: white;
                color: black;
            }
        }
        
        /* تحسينات للموبايل */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .web-header h1 {
                font-size: 1.5em;
            }
            
            .web-header p {
                font-size: 1em;
            }
        }
    </style>
</head>
<body>
    <!-- Header للنسخة الويب -->
    <div class="web-header">
        <h1>عيادة الدكتور يونس اسود الجبوري</h1>
        <p>نظام إدارة حجوزات المرضى - النسخة الويب</p>
    </div>

    <!-- محتوى التطبيق الأصلي -->
    <div class="container">
        <h1>عيادة الدكتور يونس اسود الجبوري</h1>
        
        <!-- قائمة الأيام -->
        <div id="days-container"></div>
        
        <!-- قسم الحجوزات -->
        <div class="appointments-section">
            <h2 id="selected-day-title">اختر يوماً لعرض الحجوزات</h2>
            <div id="appointments-container"></div>
            <button id="add-appointment-btn" style="display: none;">إضافة حجز</button>
        </div>
        
        <!-- أزرار التحكم -->
        <div class="controls">
            <button id="all-appointments-btn">جميع الحجوزات</button>
            <button id="export-btn">تصدير إلى Word</button>
            <button id="settings-btn">الإعدادات</button>
        </div>
    </div>

    <!-- مودال إضافة/تعديل الحجز -->
    <div id="appointment-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modal-title">إضافة حجز جديد</h2>
            <form id="appointment-form">
                <label for="patient-name">اسم المريض:</label>
                <input type="text" id="patient-name" required>
                
                <label for="session-type">نوع الجلسة:</label>
                <select id="session-type" required>
                    <option value="">اختر نوع الجلسة</option>
                    <option value="جلسة دائمية">جلسة دائمية</option>
                    <option value="جذر">جذر</option>
                </select>
                
                <div id="session-number-group" style="display: none;">
                    <label for="session-number">رقم الجلسة:</label>
                    <select id="session-number">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                    </select>
                </div>
                
                <label for="note">ملاحظة (اختياري):</label>
                <textarea id="note" rows="3"></textarea>
                
                <div class="form-buttons">
                    <button type="submit">حفظ</button>
                    <button type="button" id="cancel-btn">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- مودال جميع الحجوزات -->
    <div id="all-appointments-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>جميع الحجوزات</h2>
            <input type="text" id="all-appointments-search" placeholder="البحث عن مريض...">
            <div id="all-appointments-content"></div>
        </div>
    </div>

    <!-- مودال الإعدادات -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>الإعدادات</h2>
            <div class="settings-group">
                <label for="font-size">حجم الخط:</label>
                <select id="font-size">
                    <option value="small">صغير</option>
                    <option value="medium" selected>متوسط</option>
                    <option value="large">كبير</option>
                </select>
            </div>
            <div class="settings-group">
                <label for="font-family">نوع الخط:</label>
                <select id="font-family">
                    <option value="Arial">Arial</option>
                    <option value="Tahoma" selected>Tahoma</option>
                    <option value="Cairo">Cairo</option>
                </select>
            </div>
            <div class="settings-group">
                <label for="dark-mode">الوضع الليلي:</label>
                <input type="checkbox" id="dark-mode" checked>
            </div>
        </div>
    </div>

    <!-- Footer للنسخة الويب -->
    <div class="web-footer">
        <p>© 2025 عيادة الدكتور يونس اسود الجبوري</p>
        <p>تطوير: فريق تطوير التطبيقات الطبية</p>
        <p>الإصدار: 1.0.0 - النسخة الويب</p>
    </div>

    <script src="script.js"></script>
    
    <script>
        // تحسينات خاصة بالنسخة الويب
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة معلومات النسخة الويب
            console.log('تطبيق عيادة الدكتور يونس - النسخة الويب 1.0.0');
            
            // تحسين التخزين المحلي للويب
            if (!localStorage.getItem('webVersion')) {
                localStorage.setItem('webVersion', '1.0.0');
                localStorage.setItem('installDate', new Date().toISOString());
            }
            
            // إضافة اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case 'n':
                            e.preventDefault();
                            document.getElementById('add-appointment-btn').click();
                            break;
                        case 'f':
                            e.preventDefault();
                            document.getElementById('all-appointments-btn').click();
                            break;
                        case 's':
                            e.preventDefault();
                            document.getElementById('settings-btn').click();
                            break;
                        case 'e':
                            e.preventDefault();
                            document.getElementById('export-btn').click();
                            break;
                    }
                }
            });
        });
    </script>
</body>
</html>
