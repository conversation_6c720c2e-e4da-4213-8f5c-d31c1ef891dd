/* إعادة تعيين القيم الافتراضية */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* إعداد الخط والاتجاه */
body {
  font-family: 'Poppins', sans-serif;
  direction: rtl;
  background-color: #121212;
  color: #eee;
  line-height: 1.6;
}

/* الوضع الليلي يُطبق مباشرة على body */
.dark-mode {
  background-color: #121212;
  color: #eee;
}

/* رأس الصفحة */
header {
  background: linear-gradient(90deg, #072342, #00B4DB);
  padding: 20px;
  text-align: center;
  color: #fff;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}
h1 {
  font-size: 1.8em;
}

/* زر الإعدادات ولوحة الإعدادات */
#settingsBtn {
  display: none;
  position: fixed;
  top: 10px;
  right: 10px;
  padding: 10px 15px;
  background-color: #333;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  z-index: 500;
  transition: background-color 0.3s;
}
#settingsBtn:hover {
  background-color: #555;
}

.settings-panel {
  position: fixed;
  top: 60px;
  right: 10px;
  background-color: #1f1f1f;
  padding: 15px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  z-index: 500;
  color: #eee;
}
.setting-item {
  margin-bottom: 10px;
}
.hidden {
  display: none;
}

/* قائمة الأيام */
#days-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  margin: 20px 10px;
}
.day-button {
  background-color: #2c2c2c;
  border: 1px solid #444;
  padding: 12px;
  width: 120px;
  cursor: pointer;
  border-radius: 8px;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
  color: #eee;
}
.day-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}
.day-button.selected {
  background-color: #00aaff;
  border-color: #00aaff;
}

/* زر عرض جميع الحجوزات */
.all-appointments-btn {
  background-color: #2c2c2c;
  border: 1px solid #444;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
  color: #eee;
  margin: 10px auto;
  display: block;
}
.all-appointments-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* شريط البحث */
.search-container {
  text-align: center;
  margin: 5px 0;
}
#search-input {
  width: 80%;
  max-width: 400px;
  padding: 8px 10px;
  border: 1px solid #444;
  border-radius: 4px;
  background-color: #1f1f1f;
  color: #eee;
}

/* عرض الحجوزات */
.appointments-display {
  display: flex ;
  flex-direction: column;
  gap: 3px;
  padding: 5px 15px;
  background-color: #1f1f1f;
  border: 1px solid #444;
  margin: 5px auto 20px auto;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  min-height: 100%  ;
}
.appointments-display p {
  margin: 0;
  color: #aaa;
}

/* بطاقة الحجز */
.appointment {
  display: flex;
  align-items: center;
  background-color: #2c2c2c;
  padding: 2px;
  border: 1px solid #444;
  border-radius: 6px;
  gap: 12px;
  width: 100%;
  transition: box-shadow 0.2s;
}
.appointment:hover {
  background-color: #333;
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}
.sequence {
  font-weight: 600;
  margin-right: 5px;
  width: 5px;
}

/* حاوية الإجراء */
.procedure-container {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}
.syringe {
  cursor: pointer;
  font-size: 1.2em;
  margin-right: 2px;
}
.syringe:hover{
  border-radius: 10px;
  color: #072342;
  background-color: #444;
}
.procedure-number-display {
  font-size: 0.9em;
  font-weight: 600;
  color: #00aaff;
  margin: 0;
  border: 0;
  width: 5px;
}
.procedure-number-display:hover {
  background-color: #333;
}

/* نافذة اختيار رقم الإجراء */
.procedure-popup {
  position: absolute;
  top: 0;
  left: 100%;
  background-color: #2c2c2c;
  border: 1px solid #444;
  padding: 5px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}
.procedure-popup:hover {
column-fill: #333;

}

/* حاوية معلومات المريض */
.info-container {
  flex: 1;
  display: flex;
  padding: 1px 4px;
  border-radius: 4px;
  cursor: pointer;
  gap: 1px;
  font-size: 0.9em;
  background-color: transparent;
}

.patient-name {
  font-size: 1.1em;
  font-weight: 500;
  width: 250px;
}
.details {
  font-size: 1.1em;
  color: #ccc;
}

/* تنسيق مربعات النص */
input[type="text"],
textarea,
select {
  width: 100%;
  padding: 8px 10px;
  margin-bottom: 10px;
  border: 1px solid #444;
  border-radius: 4px;
  background-color: #1f1f1f;
  color: #eee;
}
textarea {
  resize: vertical;
}

/* تنسيق المودالات */
.modal {
  display: none;
  position: fixed;
  z-index: 200;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.5);
  animation: fadeIn 0.3s;
}
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}
.modal-content {
  background-color: #2c2c2c;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 320px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0,0,0,0.4);
  animation: slideDown 0.3s;
  color: #eee;
}
@keyframes slideDown {
  from {transform: translateY(-20px); opacity: 0;}
  to {transform: translateY(0); opacity: 1;}
}
.close-button {
  color: #aaa;
  float: left;
  font-size: 28px;
  font-weight: 600;
  cursor: pointer;
}
.close-button:hover {
  color: #fff;
}
.submit-btn {
  background-color: #00aaff;
  border: none;
  color: #fff;
  padding: 10px;
  width: 100%;
  margin-top: 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: background 0.3s;
}
.submit-btn:hover {
  background-color: #0088cc;
}

/* أزرار التحكم */
.button-container {
  text-align: center;
  margin-bottom: 20px;
}
#add-appointment-btn,
#export-word-btn {
  background-color: #28a745;
  border: none;
  color: #fff;
  padding: 10px 20px;
  font-size: 1em;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s;
  margin: 5px;
}
#add-appointment-btn:hover,
#export-word-btn:hover {
  background-color: #1e7e34;
}

/* تنسيق أزرار التعديل والحذف داخل بطاقة الحجز */
.btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1em;
  color: #eee;
  margin: 0px 10px;

}
.btn.edit:hover {
  color: #00aaff;
}
.btn.delete:hover {
  color: red;
}

/* تنسيق بطاقة الحجز في مودال "جميع الحجوزات" */
.appointment-card {
  border: 1px solid #444;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 10px;
  color: #eee;
}
