<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#2c2c2c">
    
    <title>عيادة الدكتور يونس اسود الجبوري</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="assets/icon-192.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/icon-180.png">
    
    <link rel="stylesheet" href="styles-mobile.css">
</head>
<body>
    <!-- Splash Screen -->
    <div id="splash-screen" class="splash-screen">
        <div class="splash-content">
            <img src="assets/icon-192.png" alt="شعار العيادة" class="splash-logo">
            <h1>عيادة الدكتور يونس اسود الجبوري</h1>
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- Main App -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <h1>عيادة الدكتور يونس اسود الجبوري</h1>
            <div class="header-actions">
                <button id="menu-btn" class="icon-btn">☰</button>
            </div>
        </header>

        <!-- Navigation Menu -->
        <nav id="side-menu" class="side-menu">
            <div class="menu-header">
                <h2>القائمة</h2>
                <button id="close-menu" class="icon-btn">✕</button>
            </div>
            <ul class="menu-items">
                <li><a href="#" id="home-link">الرئيسية</a></li>
                <li><a href="#" id="all-appointments-link">جميع الحجوزات</a></li>
                <li><a href="#" id="export-link">تصدير البيانات</a></li>
                <li><a href="#" id="settings-link">الإعدادات</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Days Container -->
            <section class="days-section">
                <h2>الأيام</h2>
                <div id="days-container" class="days-container-mobile"></div>
            </section>

            <!-- Appointments Section -->
            <section class="appointments-section">
                <div class="section-header">
                    <h2 id="selected-day-title">اختر يوماً لعرض الحجوزات</h2>
                    <button id="add-appointment-btn" class="add-btn" style="display: none;">+ إضافة حجز</button>
                </div>
                <div id="appointments-container" class="appointments-container"></div>
            </section>
        </main>

        <!-- Floating Action Button -->
        <button id="fab" class="fab" style="display: none;">+</button>
    </div>

    <!-- Modals -->
    <!-- Add/Edit Appointment Modal -->
    <div id="appointment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">إضافة حجز جديد</h3>
                <button class="close-btn" onclick="closeModal('appointment-modal')">&times;</button>
            </div>
            <form id="appointment-form" class="appointment-form">
                <div class="form-group">
                    <label for="patient-name">اسم المريض:</label>
                    <input type="text" id="patient-name" required>
                </div>
                
                <div class="form-group">
                    <label for="session-type">نوع الجلسة:</label>
                    <select id="session-type" required>
                        <option value="">اختر نوع الجلسة</option>
                        <option value="جلسة دائمية">جلسة دائمية</option>
                        <option value="جذر">جذر</option>
                    </select>
                </div>
                
                <div id="session-number-group" class="form-group" style="display: none;">
                    <label for="session-number">رقم الجلسة:</label>
                    <select id="session-number">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="note">ملاحظة (اختياري):</label>
                    <textarea id="note" rows="3"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="save-btn">حفظ</button>
                    <button type="button" class="cancel-btn" onclick="closeModal('appointment-modal')">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>الإعدادات</h3>
                <button class="close-btn" onclick="closeModal('settings-modal')">&times;</button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <label for="font-size">حجم الخط:</label>
                    <select id="font-size">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label for="theme">المظهر:</label>
                    <select id="theme">
                        <option value="dark" selected>داكن</option>
                        <option value="light">فاتح</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <button id="backup-btn" class="action-btn">نسخ احتياطي للبيانات</button>
                    <button id="restore-btn" class="action-btn">استعادة البيانات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="script-mobile.js"></script>
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('sw.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }
    </script>
</body>
</html>
