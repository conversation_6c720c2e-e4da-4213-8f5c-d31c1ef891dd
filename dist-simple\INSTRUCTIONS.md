# تعليمات التشغيل والتوزيع - تطبيق عيادة الدكتور يونس

## 🚀 طرق التشغيل السريع

### 1️⃣ التشغيل التلقائي (الأسهل)
```
انقر نقراً مزدوجاً على: run-app.bat
```
- سيتم فحص المتطلبات تلقائياً
- تثبيت التبعيات المطلوبة
- تشغيل التطبيق

### 2️⃣ النسخة الويب (بدون تثبيت)
```
انقر نقراً مزدوجاً على: web-version.html
```
- يعمل في أي متصفح
- لا يحتاج تثبيت
- حفظ محلي للبيانات

### 3️⃣ التشغيل اليدوي
```cmd
npm install electron --save-dev
npm start
```

## 📱 للأجهزة المحمولة

### إنشاء APK للأندرويد:
1. انتقل إلى مجلد `mobile/`
2. اتبع التعليمات في `BUILD_GUIDE.md`
3. استخدم Android Studio لبناء APK

### تشغيل كتطبيق ويب على الموبايل:
1. افتح `web-version.html` في متصفح الهاتف
2. أضف إلى الشاشة الرئيسية
3. سيعمل كتطبيق أصلي

## 🔧 استكشاف الأخطاء

### خطأ: "Node.js غير مثبت"
**الحل:**
1. حمّل Node.js من: https://nodejs.org
2. ثبّت النسخة LTS
3. أعد تشغيل الجهاز
4. شغّل `run-app.bat` مرة أخرى

### خطأ: "npm غير متوفر"
**الحل:**
1. أعد تثبيت Node.js
2. تأكد من إضافة Node.js إلى PATH
3. افتح موجه أوامر جديد

### خطأ: "فشل في تثبيت electron"
**الحل:**
1. تحقق من اتصال الإنترنت
2. شغّل كمدير (Run as Administrator)
3. نظّف cache: `npm cache clean --force`

### التطبيق يتوقف فجأة
**الحل:**
1. تحقق من مساحة القرص الصلب
2. أغلق برامج مكافحة الفيروسات مؤقتاً
3. شغّل من مجلد مختلف

## 📦 التوزيع على أجهزة أخرى

### للتوزيع على Windows:
1. انسخ مجلد `dist-simple` كاملاً
2. تأكد من وجود جميع الملفات
3. شارك مع المستخدمين ملف `run-app.bat`

### للتوزيع كتطبيق ويب:
1. ارفع ملف `web-version.html` على خادم ويب
2. أو شاركه مباشرة مع المستخدمين
3. يعمل offline بعد التحميل الأول

### للتوزيع على الموبايل:
1. استخدم ملفات مجلد `mobile/`
2. اتبع دليل Capacitor لبناء APK
3. أو شارك رابط النسخة الويب

## 🔒 الأمان والخصوصية

### حماية البيانات:
- جميع البيانات محفوظة محلياً
- لا يتم إرسال أي معلومات عبر الإنترنت
- النسخ الاحتياطي يدوي وآمن

### أذونات التطبيق:
- قراءة/كتابة الملفات المحلية
- الوصول إلى localStorage
- لا يحتاج أذونات شبكة

## 💾 النسخ الاحتياطي والاستعادة

### عمل نسخة احتياطية:
1. **النسخة المكتبية:** انسخ مجلد البيانات
2. **النسخة الويب:** استخدم "تصدير إلى Word"
3. احفظ الملفات في مكان آمن

### استعادة البيانات:
1. **النسخة المكتبية:** استبدل ملفات البيانات
2. **النسخة الويب:** استورد من ملف Word
3. أعد تشغيل التطبيق

## 📊 مراقبة الأداء

### تحسين الأداء:
- أغلق التطبيقات الأخرى
- تأكد من وجود ذاكرة كافية (2GB+)
- نظّف ملفات temp دورياً

### مؤشرات الأداء:
- سرعة التحميل: < 3 ثوانٍ
- استهلاك الذاكرة: < 200MB
- حجم البيانات: متغير حسب الاستخدام

## 🔄 التحديثات

### فحص التحديثات:
1. تحقق من الإصدار الحالي في "حول"
2. قارن مع أحدث إصدار متوفر
3. حمّل النسخة الجديدة إذا توفرت

### تطبيق التحديثات:
1. اعمل نسخة احتياطية من البيانات
2. استبدل ملفات التطبيق
3. احتفظ بملفات البيانات
4. اختبر التطبيق المحدث

## 📞 الدعم الفني

### قنوات الدعم:
- **الدعم الأولي:** راجع هذا الملف
- **المشاكل التقنية:** تواصل مع فريق التطوير
- **طلب ميزات:** اقترح التحسينات

### معلومات مطلوبة للدعم:
- نوع المشكلة
- رسالة الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة
- نظام التشغيل والإصدار

## 📋 قائمة التحقق

### قبل التوزيع:
- [ ] اختبار التطبيق على أجهزة مختلفة
- [ ] التأكد من عمل جميع الوظائف
- [ ] فحص الأمان والخصوصية
- [ ] إعداد تعليمات واضحة

### بعد التوزيع:
- [ ] متابعة ملاحظات المستخدمين
- [ ] إصلاح المشاكل المكتشفة
- [ ] تحديث التوثيق حسب الحاجة
- [ ] تخطيط للتحديثات المستقبلية

---

**ملاحظة:** هذا التطبيق مطور خصيصاً لعيادة الدكتور يونس اسود الجبوري
**الإصدار:** 1.0.0 | **التاريخ:** يوليو 2025
