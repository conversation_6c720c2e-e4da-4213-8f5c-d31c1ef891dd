<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>عيادة دكتور يونس اسود الجبوري</title>
  <!-- تضمين Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- تضمين خط Poppins -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <header>
    <h1>عيادة الدكتور يونس اسود الجبوري</h1>
    <!-- زر الإعدادات -->
    <button id="settingsBtn"><i class="fas fa-cog"></i> إعدادات</button>
  </header>
  <!-- لوحة الإعدادات -->
  <div id="settingsPanel" class="settings-panel hidden">
    <div class="setting-item">
      <label for="fontSize">حجم الخط:</label>
      <input type="range" id="fontSize" min="12" max="30" value="16">
    </div>
    <div class="setting-item">
      <label for="fontFamily">نوع الخط:</label>
      <select id="fontFamily">
        <option value="'Poppins', sans-serif">Poppins</option>
        <option value="Arial, sans-serif">Arial</option>
        <option value="Tahoma, sans-serif">Tahoma</option>
        <option value="'Cairo', sans-serif">Cairo</option>
        <option value="'Traditional Arabic', sans-serif">Traditional Arabic</option>
      </select>
    </div>
    <div class="setting-item">
      <label for="darkMode">الوضع الليلي:</label>
      <input type="checkbox" id="darkMode">
    </div>
  </div>

  <!-- قائمة الأيام -->
  <div id="days-container"></div>
  <!-- زر عرض جميع الحجوزات -->
 
  
  <!-- شريط البحث -->
  <div class="search-container">
    <input type="text" id="search-input" placeholder="ابحث عن اسم المريض...">
  </div>
  
  <!-- عرض الحجوزات لليوم المحدد -->
  <div id="appointments-display" class="appointments-display">
    <p>اختر يومًا لعرض الحجوزات</p>
  </div>
  
  <!-- أزرار التحكم -->
  <div class="button-container">
    <button id="add-appointment-btn"><i class="fas fa-plus"></i> إضافة حجز</button>
    <button id="export-word-btn"><i class="fas fa-file-export"></i> تصدير للحجوزات إلى وورد</button>
  </div>
  <button id="show-all-btn" class="all-appointments-btn"><i class="fas fa-list"></i> عرض جميع الحجوزات</button>
  
  <!-- مودال إضافة حجز -->
  <div id="appointment-modal" class="modal">
    <div class="modal-content">
      <span class="close-button" id="add-close-button">&times;</span>
      <h2>إضافة حجز</h2>
      <form id="appointment-form">
        <label for="patient-name">اسم المريض:</label>
        <input type="text" id="patient-name" placeholder="أدخل اسم المريض" required>
        
        <label for="appointment-note">ملاحظة:</label>
        <textarea id="appointment-note" placeholder="أدخل ملاحظة" rows="3"></textarea>
        
        <label for="session-type">نوع الجلسة:</label>
        <select id="session-type" required>
          <option value="غير محدد">غير محدد</option>
          <option value="دائمية">جلسة دائمية</option>
          <option value="جذر">جذر</option>
        </select>
        
        <!-- خانة رقم الجلسة -->
        <div id="root-session-number" class="input-group" style="display: none;">
          <label for="session-number">رقم الجلسة:</label>
          <select id="session-number">
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
          </select>
        </div>
        <!-- تخزين اليوم المختار -->
        <input type="hidden" id="selected-day">
        <button type="submit" class="submit-btn">إضافة</button>
      </form>
    </div>
  </div>
  
  <!-- مودال تعديل الحجز -->
  <div id="edit-modal" class="modal">
    <div class="modal-content">
      <span class="close-button" id="edit-close-button">&times;</span>
      <h2>تعديل الحجز</h2>
      <form id="edit-form">
        <label for="edit-patient-name">اسم المريض:</label>
        <input type="text" id="edit-patient-name" required>
        
        <label for="edit-appointment-note">ملاحظة:</label>
        <textarea id="edit-appointment-note" rows="3"></textarea>
        
        <label for="edit-session-type">نوع الجلسة:</label>
        <select id="edit-session-type" required>
          <option value="غير محدد">غير محدد</option>
          <option value="دائمية">جلسة دائمية</option>
          <option value="جذر">جذر</option>
        </select>
        
        <!-- خانة رقم الجلسة للتعديل -->
        <div id="edit-root-session-number" class="input-group" style="display: none;">
          <label for="edit-session-number">رقم الجلسة:</label>
          <select id="edit-session-number">
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
          </select>
        </div>
        <button type="submit" class="submit-btn">حفظ التعديلات</button>
      </form>
    </div>
  </div>
  
  <!-- مودال عرض جميع الحجوزات مع حقل البحث -->
  <div id="all-appointments-modal" class="modal">
    <div class="modal-content">
      <span class="close-button" id="all-close-button">&times;</span>
      <h2>جميع الحجوزات</h2>
      <input type="text" id="all-appointments-search" placeholder="ابحث عن اسم المريض..." style="width:100%; padding:8px; margin-bottom:10px; border:1px solid #444; border-radius:4px; background-color:#1f1f1f; color:#eee;">
      <div id="all-appointments-content"></div>
    </div>
  </div>
  
  <script src="script.js"></script>
</body>
</html>
