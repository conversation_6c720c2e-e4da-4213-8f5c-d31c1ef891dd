{"name": "dr-clinic-app", "version": "1.0.0", "description": "تطبيق إدارة حجوزات عيادة الدكتور يونس اسود الجبوري", "main": "main.js", "homepage": "./", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-linux": "electron-builder --linux", "build-mac": "electron-builder --mac", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir"}, "keywords": ["clinic", "appointments", "medical", "doctor", "عيادة", "حجوزات"], "author": {"name": "Dr. <PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^22.3.27", "electron-builder": "^24.6.4"}, "build": {"appId": "com.clinic.dr-appointments", "productName": "عيادة الدكتور يونس اسود الجبوري", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/electron/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "عيادة الدكتور يونس"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}}}