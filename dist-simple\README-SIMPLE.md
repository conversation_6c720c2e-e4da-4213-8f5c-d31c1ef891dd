# تطبيق عيادة الدكتور يونس اسود الجبوري - النسخة المبسطة

## نظرة عامة
هذه نسخة مبسطة وجاهزة للتوزيع من تطبيق إدارة حجوزات عيادة الدكتور يونس اسود الجبوري.

## محتويات المجلد

### الملفات الأساسية:
- `index.html` - الواجهة الرئيسية للتطبيق
- `script.js` - منطق التطبيق والوظائف
- `styles.css` - تنسيقات التطبيق
- `main.js` - ملف Electron الرئيسي
- `package.json` - إعدادات المشروع والتبعيات

### ملفات التشغيل:
- `run-app.bat` - ملف تشغيل تلقائي للتطبيق
- `web-version.html` - نسخة ويب مستقلة

### المجلدات:
- `assets/` - الأيقونات والصور

## طرق التشغيل

### الطريقة الأولى: التشغيل التلقائي (الأسهل)
1. انقر نقراً مزدوجاً على ملف `run-app.bat`
2. سيقوم الملف بفحص المتطلبات وتثبيت التبعيات تلقائياً
3. سيتم تشغيل التطبيق بعد انتهاء التثبيت

### الطريقة الثانية: التشغيل اليدوي
1. افتح موجه الأوامر (Command Prompt) في هذا المجلد
2. نفذ الأوامر التالية:
   ```cmd
   npm install electron --save-dev
   npm start
   ```

### الطريقة الثالثة: النسخة الويب
1. افتح ملف `web-version.html` في أي متصفح ويب
2. يعمل التطبيق مباشرة بدون الحاجة لتثبيت أي شيء
3. جميع البيانات تُحفظ في المتصفح محلياً

## المتطلبات

### للتشغيل كتطبيق سطح المكتب:
- Windows 10 أو أحدث
- Node.js (سيتم تثبيته تلقائياً إذا لم يكن موجوداً)
- اتصال بالإنترنت (للتثبيت الأولي فقط)

### للتشغيل كتطبيق ويب:
- أي متصفح ويب حديث (Chrome, Firefox, Edge, Safari)
- لا يحتاج اتصال بالإنترنت

## الميزات

### ✅ إدارة المواعيد:
- عرض 8 أيام (اليوم الحالي + 7 أيام قادمة)
- تقسيم السبت والأربعاء إلى فترتين (صباحاً/مساءً)
- إضافة وتعديل وحذف الحجوزات

### ✅ أنواع الجلسات:
- جلسة دائمية
- جذر (مع رقم الجلسة 1-4)

### ✅ نظام الألوان:
- شفاف: حجز جديد
- أخضر: تم الانتهاء
- أصفر: قيد المعالجة
- أحمر: ملغي أو مؤجل

### ✅ البحث والتصدير:
- البحث عن المرضى بالاسم
- تصدير البيانات إلى ملف Word
- عرض جميع الحجوزات

### ✅ الإعدادات:
- تخصيص حجم ونوع الخط
- الوضع الليلي/النهاري
- حفظ تلقائي للبيانات

## استكشاف الأخطاء

### مشكلة: التطبيق لا يبدأ
**الحل:**
1. تأكد من وجود Node.js على الجهاز
2. شغّل `run-app.bat` كمدير
3. تحقق من اتصال الإنترنت

### مشكلة: خطأ في تثبيت التبعيات
**الحل:**
1. احذف مجلد `node_modules` إن وُجد
2. شغّل الأمر: `npm cache clean --force`
3. أعد تشغيل `run-app.bat`

### مشكلة: البيانات لا تُحفظ
**الحل:**
1. تأكد من أن التطبيق يعمل بصلاحيات كافية
2. تحقق من مساحة القرص الصلب
3. في النسخة الويب، تأكد من تفعيل cookies

## النسخ الاحتياطي

### للنسخة المكتبية:
- البيانات محفوظة في مجلد التطبيق
- انسخ الملف `appointments.json` إن وُجد

### للنسخة الويب:
- استخدم ميزة "تصدير إلى Word" لحفظ البيانات
- البيانات محفوظة في localStorage المتصفح

## الدعم الفني

### للحصول على المساعدة:
1. راجع هذا الملف أولاً
2. تحقق من وجود تحديثات
3. تواصل مع فريق الدعم مع تفاصيل المشكلة

### معلومات النسخة:
- **الإصدار:** 1.0.0
- **تاريخ الإصدار:** يوليو 2025
- **نوع النسخة:** مبسطة للتوزيع
- **الحجم:** ~5 ميجابايت

## ملاحظات مهمة

1. **الأمان:** جميع البيانات محفوظة محلياً على جهازك
2. **الخصوصية:** لا يتم إرسال أي بيانات عبر الإنترنت
3. **التحديثات:** تحقق من وجود إصدارات جديدة دورياً
4. **النسخ الاحتياطي:** احرص على عمل نسخة احتياطية من البيانات

## الترخيص
هذا التطبيق مطور خصيصاً لعيادة الدكتور يونس اسود الجبوري.
جميع الحقوق محفوظة © 2025
